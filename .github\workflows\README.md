# GitHub Actions 工作流说明

本目录包含了用于自动化构建、测试和发布 Bitwarden TOTP 解锁版本的 GitHub Actions 工作流。

## 工作流概述

### 1. `build-and-release.yml` - 构建和发布
**触发条件**:
- 推送标签 (格式: `v*`)
- 手动触发 (workflow_dispatch)

**功能**:
- 构建多个浏览器版本 (Chrome, Firefox, Edge, Safari)
- 验证 TOTP 修改是否正确应用
- 创建压缩包 (.zip 和 .tar.gz)
- 自动发布到 GitHub Releases
- 生成详细的构建信息

**使用方法**:
```bash
# 创建标签并推送来触发发布
git tag v1.0.0
git push origin v1.0.0

# 或者在 GitHub 网页上手动触发
```

### 2. `test-build.yml` - 测试构建
**触发条件**:
- 推送到 main/develop 分支
- Pull Request 到 main 分支
- 手动触发

**功能**:
- 验证 TOTP 修改完整性
- 测试多浏览器构建
- 运行代码检查和安全审计
- 验证构建输出的完整性
- 兼容性检查

**输出**:
- 测试构建的扩展包
- 构建状态报告

### 3. `check-upstream-updates.yml` - 检查上游更新
**触发条件**:
- 每日定时运行 (02:00 UTC)
- 手动触发

**功能**:
- 检查 Bitwarden 官方仓库的新版本
- 分析关键文件的变更
- 自动创建更新提醒 Issue
- 生成变更分析报告

**自动化流程**:
1. 获取上游最新版本
2. 对比关键文件变更
3. 创建或更新 GitHub Issue
4. 提供更新建议

### 4. `cache-cleanup.yml` - 缓存清理
**触发条件**:
- 每周定时运行 (周日 03:00 UTC)
- 手动触发

**功能**:
- 清理超过 7 天的构建缓存
- 清理超过 30 天的构建产物
- 释放存储空间
- 优化仓库性能

## 配置要求

### GitHub Secrets
工作流使用以下内置 secrets，无需额外配置：
- `GITHUB_TOKEN` - 用于创建 releases 和管理 issues

### 权限要求
确保 GitHub Actions 具有以下权限：
- `contents: write` - 创建 releases
- `issues: write` - 创建更新提醒 issues
- `actions: write` - 管理缓存和构建产物

## 使用指南

### 发布新版本
1. **准备代码**:
   ```bash
   # 确保所有修改已提交
   git add .
   git commit -m "Update TOTP modifications for version X.X.X"
   git push origin main
   ```

2. **创建发布**:
   ```bash
   # 方法1: 使用标签触发
   git tag v1.0.0
   git push origin v1.0.0
   
   # 方法2: 在 GitHub 网页上手动触发
   # Actions -> Build and Release -> Run workflow
   ```

3. **验证发布**:
   - 检查 Actions 页面的构建状态
   - 确认 Releases 页面有新版本
   - 下载并测试生成的扩展包

### 测试构建
```bash
# 推送代码会自动触发测试
git push origin main

# 或手动触发测试
# Actions -> Test Build -> Run workflow
```

### 检查更新
```bash
# 手动检查上游更新
# Actions -> Check Upstream Updates -> Run workflow

# 查看自动创建的更新 Issues
# Issues -> Label: upstream-update
```

## 构建产物

### 发布文件
每次发布会生成以下文件：
- `bitwarden-totp-unlocked-chrome-{version}.zip` - Chrome 扩展
- `bitwarden-totp-unlocked-firefox-{version}.zip` - Firefox 扩展
- `bitwarden-totp-unlocked-edge-{version}.zip` - Edge 扩展
- `bitwarden-totp-unlocked-safari-{version}.zip` - Safari 扩展
- `*.tar.gz` - 备用压缩格式
- `build-summary.json` - 构建信息摘要

### 测试产物
测试构建会生成：
- `test-build-{browser}.zip` - 测试版扩展包
- 构建日志和验证报告

## 故障排除

### 构建失败
1. **检查修改完整性**:
   ```bash
   # 运行本地验证脚本
   bash .github/workflows/verify_modifications.sh
   ```

2. **检查依赖问题**:
   ```bash
   # 清理并重新安装依赖
   rm -rf node_modules package-lock.json
   npm install --force
   ```

3. **检查 Node.js 版本**:
   ```bash
   # 确保使用正确的 Node.js 版本
   node --version  # 应该是 18.x
   ```

### 发布失败
1. **检查权限**: 确保仓库有 Actions 写权限
2. **检查标签**: 确保标签格式正确 (`v1.0.0`)
3. **检查文件大小**: GitHub 有文件大小限制

### 更新检查失败
1. **检查网络**: 确保能访问上游仓库
2. **检查权限**: 确保能创建 Issues
3. **手动运行**: 使用 workflow_dispatch 手动触发

## 自定义配置

### 修改构建矩阵
在 `build-and-release.yml` 中修改：
```yaml
strategy:
  matrix:
    browser: [chrome, firefox, edge]  # 移除不需要的浏览器
```

### 修改检查频率
在 `check-upstream-updates.yml` 中修改：
```yaml
schedule:
  - cron: '0 6 * * *'  # 改为每日 06:00 UTC
```

### 修改缓存保留时间
在 `cache-cleanup.yml` 中修改：
```javascript
cutoffDate.setDate(cutoffDate.getDate() - 14);  // 保留 14 天
```

## 最佳实践

1. **定期检查**: 每周查看 Actions 运行状态
2. **及时更新**: 收到更新提醒后及时处理
3. **测试验证**: 每次发布前先运行测试构建
4. **备份重要**: 保留重要版本的备份
5. **文档更新**: 修改工作流后更新此文档

## 监控和维护

### 定期检查项目
- [ ] Actions 运行状态
- [ ] 存储空间使用情况
- [ ] 上游更新提醒
- [ ] 构建产物完整性

### 维护任务
- [ ] 每月检查依赖更新
- [ ] 每季度检查工作流优化
- [ ] 及时处理安全警告
- [ ] 更新文档和说明

---
**注意**: 这些工作流是为 Bitwarden TOTP 解锁项目特别设计的，使用时请根据实际需求进行调整。
