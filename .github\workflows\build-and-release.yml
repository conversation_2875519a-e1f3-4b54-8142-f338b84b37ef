name: Build and Release Bitwarden TOTP Unlocked

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        default: 'v1.0.0'
      create_release:
        description: 'Create GitHub Release'
        required: true
        default: true
        type: boolean

env:
  NODE_VERSION: '18'
  CACHE_VERSION: 'v1'

jobs:
  build:
    name: Build Extensions
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chrome, firefox, edge, safari]
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Cache node modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            apps/browser/node_modules
            ~/.npm
          key: ${{ runner.os }}-node-${{ env.CACHE_VERSION }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-${{ env.CACHE_VERSION }}-
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: |
          npm ci --force
          cd apps/browser
          npm ci --force

      - name: Verify TOTP modifications
        run: |
          echo "Verifying TOTP modifications are applied..."
          
          # Check billing service modification
          if grep -q "// Always return true to enable TOTP for all users" libs/common/src/billing/services/account/billing-account-profile-state.service.ts; then
            echo "✅ Billing service modification found"
          else
            echo "❌ Billing service modification missing"
            exit 1
          fi
          
          # Check copy service modification
          if grep -q "// MODIFIED: Simply check if cipher has TOTP, bypass premium restrictions" libs/vault/src/services/copy-cipher-field.service.ts; then
            echo "✅ Copy service modification found"
          else
            echo "❌ Copy service modification missing"
            exit 1
          fi
          
          # Check UI modification
          if grep -q "<!-- MODIFIED: Removed premium badge requirement -->" libs/vault/src/cipher-view/login-credentials/login-credentials-view.component.html; then
            echo "✅ UI modification found"
          else
            echo "❌ UI modification missing"
            exit 1
          fi
          
          echo "All TOTP modifications verified successfully!"

      - name: Build ${{ matrix.browser }} extension
        run: |
          cd apps/browser
          case "${{ matrix.browser }}" in
            chrome)
              npm run build:prod:chrome
              ;;
            firefox)
              npm run build:prod:firefox
              ;;
            edge)
              npm run build:prod:edge
              ;;
            safari)
              npm run build:prod:safari
              ;;
          esac

      - name: Create extension package
        run: |
          cd apps/browser
          
          # Get version from package.json or input
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION="${{ github.ref_name }}"
          fi
          
          # Remove 'v' prefix if present
          VERSION=${VERSION#v}
          
          # Create package name
          PACKAGE_NAME="bitwarden-totp-unlocked-${{ matrix.browser }}-${VERSION}"
          
          # Create zip package
          cd build
          zip -r "../${PACKAGE_NAME}.zip" .
          
          # Create tar.gz package for backup
          tar -czf "../${PACKAGE_NAME}.tar.gz" .
          
          cd ..
          echo "PACKAGE_NAME=${PACKAGE_NAME}" >> $GITHUB_ENV
          echo "VERSION=${VERSION}" >> $GITHUB_ENV

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PACKAGE_NAME }}
          path: |
            apps/browser/${{ env.PACKAGE_NAME }}.zip
            apps/browser/${{ env.PACKAGE_NAME }}.tar.gz
          retention-days: 30

      - name: Generate build info
        run: |
          cd apps/browser
          cat > build-info-${{ matrix.browser }}.json << EOF
          {
            "browser": "${{ matrix.browser }}",
            "version": "${{ env.VERSION }}",
            "build_date": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "commit_sha": "${{ github.sha }}",
            "commit_ref": "${{ github.ref }}",
            "package_name": "${{ env.PACKAGE_NAME }}",
            "file_size_zip": $(stat -c%s "${{ env.PACKAGE_NAME }}.zip" 2>/dev/null || echo "0"),
            "file_size_tar": $(stat -c%s "${{ env.PACKAGE_NAME }}.tar.gz" 2>/dev/null || echo "0")
          }
          EOF

      - name: Upload build info
        uses: actions/upload-artifact@v4
        with:
          name: build-info-${{ matrix.browser }}
          path: apps/browser/build-info-${{ matrix.browser }}.json

  release:
    name: Create Release
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && github.event.inputs.create_release == 'true')
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Prepare release assets
        run: |
          mkdir -p release-assets
          
          # Move all zip and tar.gz files to release assets
          find artifacts -name "*.zip" -exec cp {} release-assets/ \;
          find artifacts -name "*.tar.gz" -exec cp {} release-assets/ \;
          
          # Collect build info
          find artifacts -name "build-info-*.json" -exec cp {} release-assets/ \;
          
          # Create combined build info
          echo "{" > release-assets/build-summary.json
          echo "  \"builds\": [" >> release-assets/build-summary.json
          
          first=true
          for info_file in release-assets/build-info-*.json; do
            if [ "$first" = true ]; then
              first=false
            else
              echo "," >> release-assets/build-summary.json
            fi
            cat "$info_file" >> release-assets/build-summary.json
          done
          
          echo "  ]," >> release-assets/build-summary.json
          echo "  \"total_builds\": $(ls release-assets/build-info-*.json | wc -l)," >> release-assets/build-summary.json
          echo "  \"release_date\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" >> release-assets/build-summary.json
          echo "}" >> release-assets/build-summary.json
          
          # List all files
          echo "Release assets:"
          ls -la release-assets/

      - name: Get version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION="${{ github.ref_name }}"
          fi
          echo "RELEASE_VERSION=${VERSION}" >> $GITHUB_ENV

      - name: Generate release notes
        run: |
          cat > release-notes.md << 'EOF'
          # Bitwarden TOTP Unlocked ${{ env.RELEASE_VERSION }}
          
          ## 🎉 功能特性
          - ✅ **完全解锁 TOTP 功能**：免费用户可使用所有 TOTP 相关功能
          - ✅ **多浏览器支持**：Chrome、Firefox、Edge、Safari
          - ✅ **保持原有功能**：不影响其他 Bitwarden 功能的正常使用
          - ✅ **UI 完整性**：移除所有付费限制提示
          - ✅ **自动填充支持**：TOTP 代码可用于自动填充
          
          ## 📦 下载文件
          
          ### Chrome 扩展
          - `bitwarden-totp-unlocked-chrome-*.zip` - Chrome 浏览器扩展
          
          ### Firefox 扩展
          - `bitwarden-totp-unlocked-firefox-*.zip` - Firefox 浏览器扩展
          
          ### Edge 扩展
          - `bitwarden-totp-unlocked-edge-*.zip` - Microsoft Edge 扩展
          
          ### Safari 扩展
          - `bitwarden-totp-unlocked-safari-*.zip` - Safari 浏览器扩展
          
          ### 备用格式
          - `*.tar.gz` - 备用压缩格式
          
          ## 🚀 安装方法
          
          1. 下载对应浏览器的 `.zip` 文件
          2. 解压到任意文件夹
          3. 在浏览器中打开扩展管理页面
          4. 开启"开发者模式"
          5. 点击"加载已解压的扩展程序"
          6. 选择解压后的文件夹
          
          ## ⚠️ 注意事项
          
          - 这是修改版本，不是官方版本
          - 请确保从可信来源获取
          - 建议定期备份 Bitwarden 数据
          - 仅供学习和研究目的
          
          ## 🔧 技术信息
          
          - **基于版本**: Bitwarden 2025.6.0
          - **构建时间**: $(date -u +%Y-%m-%d\ %H:%M:%S\ UTC)
          - **提交哈希**: ${{ github.sha }}
          
          ## 📋 修改内容
          
          详细的修改信息请参考项目中的 `TOTP_MODIFICATION_GUIDE.md` 文件。
          
          ---
          **免责声明**: 此修改版本仅供学习研究使用，请支持官方正版软件。
          EOF

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ env.RELEASE_VERSION }}
          name: Bitwarden TOTP Unlocked ${{ env.RELEASE_VERSION }}
          body_path: release-notes.md
          files: |
            release-assets/*.zip
            release-assets/*.tar.gz
            release-assets/build-summary.json
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  notify:
    name: Build Notification
    needs: [build, release]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Notify build status
        run: |
          if [ "${{ needs.build.result }}" = "success" ] && [ "${{ needs.release.result }}" = "success" ]; then
            echo "✅ Build and release completed successfully!"
          elif [ "${{ needs.build.result }}" = "success" ]; then
            echo "✅ Build completed successfully, but release was skipped or failed"
          else
            echo "❌ Build failed"
            exit 1
          fi
