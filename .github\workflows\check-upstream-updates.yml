name: Check Upstream Updates

on:
  schedule:
    # Run every day at 02:00 UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

jobs:
  check-updates:
    name: Check for Bitwarden Updates
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Git
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

      - name: Add upstream remote
        run: |
          git remote add upstream https://github.com/bitwarden/clients.git || true
          git fetch upstream

      - name: Check for new releases
        id: check_release
        run: |
          echo "Checking for new Bitwarden releases..."
          
          # Get latest upstream tag
          latest_upstream=$(git ls-remote --tags upstream | grep -E 'refs/tags/browser-v[0-9]+\.[0-9]+\.[0-9]+$' | sort -V | tail -1 | sed 's/.*refs\/tags\///')
          
          # Get our latest tag
          latest_local=$(git tag -l 'v*' | sort -V | tail -1)
          
          echo "Latest upstream release: $latest_upstream"
          echo "Latest local release: $latest_local"
          
          echo "latest_upstream=$latest_upstream" >> $GITHUB_OUTPUT
          echo "latest_local=$latest_local" >> $GITHUB_OUTPUT
          
          # Extract version numbers for comparison
          upstream_version=$(echo $latest_upstream | sed 's/browser-v//')
          local_version=$(echo $latest_local | sed 's/v//')
          
          echo "upstream_version=$upstream_version" >> $GITHUB_OUTPUT
          echo "local_version=$local_version" >> $GITHUB_OUTPUT
          
          # Simple version comparison
          if [ "$upstream_version" != "$local_version" ]; then
            echo "new_version_available=true" >> $GITHUB_OUTPUT
            echo "🆕 New version available: $upstream_version (current: $local_version)"
          else
            echo "new_version_available=false" >> $GITHUB_OUTPUT
            echo "✅ Already up to date"
          fi

      - name: Check critical files changes
        id: check_files
        if: steps.check_release.outputs.new_version_available == 'true'
        run: |
          echo "Checking for changes in critical files..."
          
          # Define critical files that contain our modifications
          critical_files=(
            "libs/common/src/billing/services/account/billing-account-profile-state.service.ts"
            "libs/vault/src/services/copy-cipher-field.service.ts"
            "libs/vault/src/cipher-view/login-credentials/login-credentials-view.component.html"
            "libs/angular/src/vault/components/view.component.ts"
            "apps/browser/src/autofill/services/autofill.service.ts"
          )
          
          changed_files=()
          
          # Compare with upstream
          for file in "${critical_files[@]}"; do
            if git diff HEAD upstream/main -- "$file" | grep -q .; then
              echo "📝 Changes detected in: $file"
              changed_files+=("$file")
            else
              echo "✅ No changes in: $file"
            fi
          done
          
          if [ ${#changed_files[@]} -gt 0 ]; then
            echo "files_changed=true" >> $GITHUB_OUTPUT
            echo "changed_files=${changed_files[*]}" >> $GITHUB_OUTPUT
            echo "⚠️  ${#changed_files[@]} critical files have changes"
          else
            echo "files_changed=false" >> $GITHUB_OUTPUT
            echo "✅ No changes in critical files"
          fi

      - name: Analyze changes
        id: analyze
        if: steps.check_files.outputs.files_changed == 'true'
        run: |
          echo "Analyzing changes in critical files..."
          
          # Create detailed change report
          echo "# Change Analysis Report" > change_report.md
          echo "" >> change_report.md
          echo "**New upstream version**: ${{ steps.check_release.outputs.upstream_version }}" >> change_report.md
          echo "**Current version**: ${{ steps.check_release.outputs.local_version }}" >> change_report.md
          echo "" >> change_report.md
          echo "## Changed Files" >> change_report.md
          echo "" >> change_report.md
          
          IFS=' ' read -ra files <<< "${{ steps.check_files.outputs.changed_files }}"
          for file in "${files[@]}"; do
            echo "### $file" >> change_report.md
            echo "" >> change_report.md
            echo '```diff' >> change_report.md
            git diff HEAD upstream/main -- "$file" | head -50 >> change_report.md
            echo '```' >> change_report.md
            echo "" >> change_report.md
          done
          
          echo "## Recommendations" >> change_report.md
          echo "" >> change_report.md
          echo "1. Review the changes above carefully" >> change_report.md
          echo "2. Check if our TOTP modifications are still compatible" >> change_report.md
          echo "3. Update modifications if necessary" >> change_report.md
          echo "4. Test the updated version thoroughly" >> change_report.md
          echo "" >> change_report.md
          echo "## Next Steps" >> change_report.md
          echo "" >> change_report.md
          echo "- [ ] Review upstream changes" >> change_report.md
          echo "- [ ] Update TOTP modifications" >> change_report.md
          echo "- [ ] Test build process" >> change_report.md
          echo "- [ ] Create new release" >> change_report.md

      - name: Create issue for updates
        if: steps.check_release.outputs.new_version_available == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            
            const newVersion = '${{ steps.check_release.outputs.upstream_version }}';
            const currentVersion = '${{ steps.check_release.outputs.local_version }}';
            const filesChanged = '${{ steps.check_files.outputs.files_changed }}' === 'true';
            
            let title = `🆕 New Bitwarden version available: ${newVersion}`;
            let body = `A new version of Bitwarden is available.\n\n`;
            body += `**New version**: ${newVersion}\n`;
            body += `**Current version**: ${currentVersion}\n\n`;
            
            if (filesChanged) {
              title += ' ⚠️ (Critical files changed)';
              body += `⚠️ **Critical files have changes** - Manual review required!\n\n`;
              
              // Read change report if it exists
              try {
                const changeReport = fs.readFileSync('change_report.md', 'utf8');
                body += changeReport;
              } catch (error) {
                body += `Could not read detailed change report: ${error.message}\n`;
              }
            } else {
              body += `✅ No changes detected in critical files containing TOTP modifications.\n\n`;
              body += `## Quick Update Steps\n\n`;
              body += `1. Merge upstream changes\n`;
              body += `2. Verify TOTP modifications are still applied\n`;
              body += `3. Run test build\n`;
              body += `4. Create new release\n`;
            }
            
            body += `\n\n---\n`;
            body += `This issue was automatically created by the upstream update checker.\n`;
            body += `Check date: ${new Date().toISOString()}`;
            
            // Check if similar issue already exists
            const { data: issues } = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              labels: 'upstream-update'
            });
            
            const existingIssue = issues.find(issue => 
              issue.title.includes(newVersion)
            );
            
            if (existingIssue) {
              console.log(`Issue already exists for version ${newVersion}: #${existingIssue.number}`);
              
              // Update existing issue with new information
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: existingIssue.number,
                body: `🔄 **Update Check**: ${new Date().toISOString()}\n\n${body}`
              });
            } else {
              // Create new issue
              const issue = await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                body: body,
                labels: ['upstream-update', filesChanged ? 'needs-review' : 'auto-update']
              });
              
              console.log(`Created issue #${issue.data.number} for version ${newVersion}`);
            }

      - name: Summary
        run: |
          echo "## Update Check Summary"
          echo "- Upstream version: ${{ steps.check_release.outputs.upstream_version }}"
          echo "- Local version: ${{ steps.check_release.outputs.local_version }}"
          echo "- New version available: ${{ steps.check_release.outputs.new_version_available }}"
          echo "- Critical files changed: ${{ steps.check_files.outputs.files_changed }}"
          
          if [ "${{ steps.check_release.outputs.new_version_available }}" = "true" ]; then
            if [ "${{ steps.check_files.outputs.files_changed }}" = "true" ]; then
              echo "⚠️  Manual review required due to critical file changes"
            else
              echo "✅ Safe to auto-update"
            fi
          else
            echo "✅ Already up to date"
          fi
