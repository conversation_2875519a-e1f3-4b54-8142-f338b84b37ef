name: Cache Cleanup

on:
  schedule:
    # Run weekly on Sunday at 03:00 UTC
    - cron: '0 3 * * 0'
  workflow_dispatch:

jobs:
  cleanup-caches:
    name: Cleanup Old Caches
    runs-on: ubuntu-latest
    
    steps:
      - name: Cleanup old caches
        uses: actions/github-script@v7
        with:
          script: |
            console.log("Starting cache cleanup...");
            
            const { data: caches } = await github.rest.actions.getActionsCaches({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 100
            });
            
            console.log(`Found ${caches.actions_caches.length} caches`);
            
            // Keep caches from the last 7 days
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 7);
            
            let deletedCount = 0;
            let totalSize = 0;
            
            for (const cache of caches.actions_caches) {
              const cacheDate = new Date(cache.created_at);
              
              if (cacheDate < cutoffDate) {
                try {
                  await github.rest.actions.deleteActionsCacheById({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    cache_id: cache.id
                  });
                  
                  console.log(`Deleted cache: ${cache.key} (${cache.size_in_bytes} bytes)`);
                  deletedCount++;
                  totalSize += cache.size_in_bytes;
                } catch (error) {
                  console.log(`Failed to delete cache ${cache.key}: ${error.message}`);
                }
              }
            }
            
            console.log(`Cleanup completed:`);
            console.log(`- Deleted ${deletedCount} caches`);
            console.log(`- Freed ${(totalSize / 1024 / 1024).toFixed(2)} MB`);

  cleanup-artifacts:
    name: Cleanup Old Artifacts
    runs-on: ubuntu-latest
    
    steps:
      - name: Cleanup old artifacts
        uses: actions/github-script@v7
        with:
          script: |
            console.log("Starting artifact cleanup...");
            
            const { data: artifacts } = await github.rest.actions.listArtifactsForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 100
            });
            
            console.log(`Found ${artifacts.artifacts.length} artifacts`);
            
            // Keep artifacts from the last 30 days
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 30);
            
            let deletedCount = 0;
            let totalSize = 0;
            
            for (const artifact of artifacts.artifacts) {
              const artifactDate = new Date(artifact.created_at);
              
              if (artifactDate < cutoffDate && !artifact.name.includes('release')) {
                try {
                  await github.rest.actions.deleteArtifact({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    artifact_id: artifact.id
                  });
                  
                  console.log(`Deleted artifact: ${artifact.name} (${artifact.size_in_bytes} bytes)`);
                  deletedCount++;
                  totalSize += artifact.size_in_bytes;
                } catch (error) {
                  console.log(`Failed to delete artifact ${artifact.name}: ${error.message}`);
                }
              }
            }
            
            console.log(`Cleanup completed:`);
            console.log(`- Deleted ${deletedCount} artifacts`);
            console.log(`- Freed ${(totalSize / 1024 / 1024).toFixed(2)} MB`);

  summary:
    name: Cleanup Summary
    needs: [cleanup-caches, cleanup-artifacts]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Summary
        run: |
          echo "## Cleanup Summary"
          echo "- Cache cleanup: ${{ needs.cleanup-caches.result }}"
          echo "- Artifact cleanup: ${{ needs.cleanup-artifacts.result }}"
          echo "- Cleanup completed at: $(date -u +%Y-%m-%d\ %H:%M:%S\ UTC)"
