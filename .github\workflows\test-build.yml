name: Test Build

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'

jobs:
  test-build:
    name: Test Build (${{ matrix.browser }})
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chrome, firefox]
      fail-fast: false
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: |
          npm ci --force
          cd apps/browser
          npm ci --force

      - name: Verify TOTP modifications
        run: |
          echo "Verifying TOTP modifications..."
          
          # Check all required modifications
          modifications_found=0
          
          if grep -q "// Always return true to enable TOTP for all users" libs/common/src/billing/services/account/billing-account-profile-state.service.ts; then
            echo "✅ Billing service modification found"
            ((modifications_found++))
          else
            echo "❌ Billing service modification missing"
          fi
          
          if grep -q "// MODIFIED: Simply check if cipher has TOTP, bypass premium restrictions" libs/vault/src/services/copy-cipher-field.service.ts; then
            echo "✅ Copy service modification found"
            ((modifications_found++))
          else
            echo "❌ Copy service modification missing"
          fi
          
          if grep -q "<!-- MODIFIED: Removed premium badge requirement -->" libs/vault/src/cipher-view/login-credentials/login-credentials-view.component.html; then
            echo "✅ UI modification found"
            ((modifications_found++))
          else
            echo "❌ UI modification missing"
          fi
          
          if grep -q "// MODIFIED: Never show premium required for TOTP" libs/angular/src/vault/components/view.component.ts; then
            echo "✅ View component modification found"
            ((modifications_found++))
          else
            echo "❌ View component modification missing"
          fi
          
          if grep -q "// MODIFIED: Removed premium restriction for TOTP" apps/browser/src/autofill/services/autofill.service.ts; then
            echo "✅ Autofill service modification found"
            ((modifications_found++))
          else
            echo "❌ Autofill service modification missing"
          fi
          
          echo "Found $modifications_found out of 5 required modifications"
          
          if [ $modifications_found -eq 5 ]; then
            echo "✅ All TOTP modifications verified successfully!"
          else
            echo "❌ Some TOTP modifications are missing!"
            exit 1
          fi

      - name: Run linting
        run: |
          cd apps/browser
          # Run basic syntax check
          npm run lint || echo "Linting completed with warnings"

      - name: Build ${{ matrix.browser }} extension
        run: |
          cd apps/browser
          echo "Building ${{ matrix.browser }} extension..."
          
          case "${{ matrix.browser }}" in
            chrome)
              npm run build:prod:chrome
              ;;
            firefox)
              npm run build:prod:firefox
              ;;
            edge)
              npm run build:prod:edge
              ;;
            safari)
              npm run build:prod:safari
              ;;
          esac

      - name: Verify build output
        run: |
          cd apps/browser/build
          
          echo "Checking build output..."
          
          # Check essential files exist
          required_files=(
            "manifest.json"
            "background.js"
            "popup/index.html"
            "popup/main.js"
            "content/autofiller.js"
          )
          
          missing_files=0
          for file in "${required_files[@]}"; do
            if [ -f "$file" ]; then
              echo "✅ Found: $file"
            else
              echo "❌ Missing: $file"
              ((missing_files++))
            fi
          done
          
          if [ $missing_files -eq 0 ]; then
            echo "✅ All essential files found in build output"
          else
            echo "❌ $missing_files essential files are missing"
            exit 1
          fi
          
          # Check manifest.json validity
          if jq empty manifest.json 2>/dev/null; then
            echo "✅ manifest.json is valid JSON"
          else
            echo "❌ manifest.json is invalid JSON"
            exit 1
          fi
          
          # Show build size
          echo "Build size information:"
          du -sh .
          echo "File count: $(find . -type f | wc -l)"

      - name: Test package creation
        run: |
          cd apps/browser/build
          
          # Create test package
          zip -r "../test-package-${{ matrix.browser }}.zip" .
          
          cd ..
          
          # Verify package
          if [ -f "test-package-${{ matrix.browser }}.zip" ]; then
            echo "✅ Package created successfully"
            echo "Package size: $(du -h test-package-${{ matrix.browser }}.zip | cut -f1)"
          else
            echo "❌ Package creation failed"
            exit 1
          fi

      - name: Upload test artifacts
        uses: actions/upload-artifact@v4
        with:
          name: test-build-${{ matrix.browser }}
          path: apps/browser/test-package-${{ matrix.browser }}.zip
          retention-days: 7

  security-check:
    name: Security Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm ci --force

      - name: Run security audit
        run: |
          echo "Running security audit..."
          npm audit --audit-level=high || echo "Security audit completed with warnings"

      - name: Check for sensitive data
        run: |
          echo "Checking for sensitive data..."
          
          # Check for potential secrets or sensitive information
          sensitive_patterns=(
            "password.*=.*['\"][^'\"]*['\"]"
            "api[_-]?key.*=.*['\"][^'\"]*['\"]"
            "secret.*=.*['\"][^'\"]*['\"]"
            "token.*=.*['\"][^'\"]*['\"]"
          )
          
          found_sensitive=false
          for pattern in "${sensitive_patterns[@]}"; do
            if grep -r -i -E "$pattern" . --exclude-dir=node_modules --exclude-dir=.git; then
              echo "⚠️  Potential sensitive data found matching pattern: $pattern"
              found_sensitive=true
            fi
          done
          
          if [ "$found_sensitive" = false ]; then
            echo "✅ No obvious sensitive data patterns found"
          else
            echo "⚠️  Please review the flagged content above"
          fi

  compatibility-check:
    name: Compatibility Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check Node.js compatibility
        run: |
          echo "Checking Node.js version compatibility..."
          
          # Check package.json for engine requirements
          if [ -f "package.json" ]; then
            if jq -e '.engines.node' package.json > /dev/null; then
              required_node=$(jq -r '.engines.node' package.json)
              echo "Required Node.js version: $required_node"
            else
              echo "No specific Node.js version requirement found"
            fi
          fi
          
          echo "Current Node.js version: $(node --version)"
          echo "Current npm version: $(npm --version)"

      - name: Check browser compatibility
        run: |
          echo "Checking browser compatibility..."
          
          # Check manifest.json for browser requirements
          if [ -f "apps/browser/src/manifest.json" ]; then
            echo "Checking manifest.json requirements..."
            
            if jq -e '.minimum_chrome_version' apps/browser/src/manifest.json > /dev/null; then
              min_chrome=$(jq -r '.minimum_chrome_version' apps/browser/src/manifest.json)
              echo "Minimum Chrome version: $min_chrome"
            fi
            
            manifest_version=$(jq -r '.manifest_version' apps/browser/src/manifest.json)
            echo "Manifest version: $manifest_version"
          fi

  summary:
    name: Test Summary
    needs: [test-build, security-check, compatibility-check]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Test Summary
        run: |
          echo "## Test Build Summary"
          echo "- Test Build: ${{ needs.test-build.result }}"
          echo "- Security Check: ${{ needs.security-check.result }}"
          echo "- Compatibility Check: ${{ needs.compatibility-check.result }}"
          
          if [ "${{ needs.test-build.result }}" = "success" ] && [ "${{ needs.security-check.result }}" = "success" ] && [ "${{ needs.compatibility-check.result }}" = "success" ]; then
            echo "✅ All tests passed successfully!"
          else
            echo "❌ Some tests failed. Please check the logs above."
            exit 1
          fi
